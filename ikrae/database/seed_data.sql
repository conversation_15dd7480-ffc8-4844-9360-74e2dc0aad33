-- بيانات أولية لنظام التواصل التعليمي المغربي
-- Initial seed data for Moroccan Educational Communication System

-- إدراج المؤسسات (التسلسل الهرمي)

-- 1. وزارة التربية الوطنية (المستوى الوطني)
INSERT INTO organizations (id, name, name_ar, code, type, parent_id) VALUES 
('00000000-0000-0000-0000-000000000001', 'Ministry of National Education', 'وزارة التربية الوطنية والتعليم الأولي والرياضة', 'MEN', 'ministry', NULL);

-- 2. الأكاديميات الجهوية (12 جهة)
INSERT INTO organizations (id, name, name_ar, code, type, parent_id) VALUES 
('00000000-0000-0000-0000-000000000011', 'Regional Academy of Casablanca-Settat', 'الأكاديمية الجهوية للتربية والتكوين لجهة الدار البيضاء سطات', 'AREF-CS', 'region', '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000012', 'Regional Academy of Rabat-Salé-Kénitra', 'الأكاديمية الجهوية للتربية والتكوين لجهة الرباط سلا القنيطرة', 'AREF-RSK', 'region', '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000013', 'Regional Academy of Fès-Meknès', 'الأكاديمية الجهوية للتربية والتكوين لجهة فاس مكناس', 'AREF-FM', 'region', '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000014', 'Regional Academy of Marrakech-Safi', 'الأكاديمية الجهوية للتربية والتكوين لجهة مراكش آسفي', 'AREF-MS', 'region', '00000000-0000-0000-0000-000000000001');

-- 3. المديريات الإقليمية (أمثلة)
INSERT INTO organizations (id, name, name_ar, code, type, parent_id) VALUES 
('00000000-0000-0000-0000-000000000111', 'Provincial Directorate of Casablanca', 'المديرية الإقليمية للدار البيضاء', 'DP-CASA', 'province', '00000000-0000-0000-0000-000000000011'),
('00000000-0000-0000-0000-000000000112', 'Provincial Directorate of Mohammedia', 'المديرية الإقليمية للمحمدية', 'DP-MOHAM', 'province', '00000000-0000-0000-0000-000000000011'),
('00000000-0000-0000-0000-000000000121', 'Provincial Directorate of Rabat', 'المديرية الإقليمية للرباط', 'DP-RABAT', 'province', '00000000-0000-0000-0000-000000000012'),
('00000000-0000-0000-0000-000000000131', 'Provincial Directorate of Fès', 'المديرية الإقليمية لفاس', 'DP-FES', 'province', '00000000-0000-0000-0000-000000000013');

-- 4. المؤسسات التعليمية (أمثلة)
INSERT INTO organizations (id, name, name_ar, code, type, parent_id, address, phone, email) VALUES 
('00000000-0000-0000-0000-000000001111', 'Hassan II High School', 'ثانوية الحسن الثاني التأهيلية', 'HS-HASSAN2', 'school', '00000000-0000-0000-0000-000000000111', 'Casablanca, Morocco', '+212522123456', '<EMAIL>'),
('00000000-0000-0000-0000-000000001112', 'Al Khawarizmi Middle School', 'إعدادية الخوارزمي', 'MS-KHAWARIZMI', 'school', '00000000-0000-0000-0000-000000000111', 'Casablanca, Morocco', '+212522123457', '<EMAIL>'),
('00000000-0000-0000-0000-000000001113', 'Ibn Sina Primary School', 'مدرسة ابن سينا الابتدائية', 'PS-IBNSINA', 'school', '00000000-0000-0000-0000-000000000111', 'Casablanca, Morocco', '+212522123458', '<EMAIL>');

-- إدراج المستخدمين الأوليين

-- 1. مسؤول وطني
INSERT INTO users (id, massar_id, email, password_hash, first_name, last_name, first_name_ar, last_name_ar, role, organization_id) VALUES 
('10000000-0000-0000-0000-000000000001', 'MEN001', '<EMAIL>', '$2b$10$example_hash_here', 'Ahmed', 'Benali', 'أحمد', 'بنعلي', 'national_admin', '00000000-0000-0000-0000-000000000001');

-- 2. مديرو الجهات
INSERT INTO users (id, massar_id, email, password_hash, first_name, last_name, first_name_ar, last_name_ar, role, organization_id) VALUES 
('10000000-0000-0000-0000-000000000011', 'AREF001', '<EMAIL>', '$2b$10$example_hash_here', 'Fatima', 'Alaoui', 'فاطمة', 'العلوي', 'regional_director', '00000000-0000-0000-0000-000000000011'),
('10000000-0000-0000-0000-000000000012', 'AREF002', '<EMAIL>', '$2b$10$example_hash_here', 'Mohammed', 'Tazi', 'محمد', 'التازي', 'regional_director', '00000000-0000-0000-0000-000000000012');

-- 3. مديرو الأقاليم
INSERT INTO users (id, massar_id, email, password_hash, first_name, last_name, first_name_ar, last_name_ar, role, organization_id) VALUES 
('10000000-0000-0000-0000-000000000111', 'DP001', '<EMAIL>', '$2b$10$example_hash_here', 'Youssef', 'Benjelloun', 'يوسف', 'بنجلون', 'provincial_director', '00000000-0000-0000-0000-000000000111'),
('10000000-0000-0000-0000-000000000121', 'DP002', '<EMAIL>', '$2b$10$example_hash_here', 'Aicha', 'Fassi', 'عائشة', 'الفاسي', 'provincial_director', '00000000-0000-0000-0000-000000000121');

-- 4. مديرو المؤسسات
INSERT INTO users (id, massar_id, email, password_hash, first_name, last_name, first_name_ar, last_name_ar, role, organization_id, phone) VALUES 
('10000000-0000-0000-0000-000000001111', 'DIR001', '<EMAIL>', '$2b$10$example_hash_here', 'Khalid', 'Amrani', 'خالد', 'العمراني', 'school_director', '00000000-0000-0000-0000-000000001111', '+212661234567'),
('10000000-0000-0000-0000-000000001112', 'DIR002', '<EMAIL>', '$2b$10$example_hash_here', 'Nadia', 'Berrada', 'نادية', 'برادة', 'school_director', '00000000-0000-0000-0000-000000001112', '+212661234568');

-- 5. أساتذة
INSERT INTO users (id, massar_id, email, password_hash, first_name, last_name, first_name_ar, last_name_ar, role, organization_id, phone) VALUES 
('10000000-0000-0000-0000-000000002111', 'T001001', '<EMAIL>', '$2b$10$example_hash_here', 'Omar', 'Idrissi', 'عمر', 'الإدريسي', 'teacher', '00000000-0000-0000-0000-000000001111', '+212662345678'),
('10000000-0000-0000-0000-000000002112', 'T001002', '<EMAIL>', '$2b$10$example_hash_here', 'Khadija', 'Bennani', 'خديجة', 'بناني', 'teacher', '00000000-0000-0000-0000-000000001111', '+212662345679'),
('10000000-0000-0000-0000-000000002121', 'T002001', '<EMAIL>', '$2b$10$example_hash_here', 'Rachid', 'Zouani', 'رشيد', 'الزواني', 'teacher', '00000000-0000-0000-0000-000000001112', '+212662345680');

-- 6. تلاميذ (أمثلة)
INSERT INTO users (id, massar_id, email, password_hash, first_name, last_name, first_name_ar, last_name_ar, role, organization_id) VALUES 
('10000000-0000-0000-0000-000000003111', 'S001001', '<EMAIL>', '$2b$10$example_hash_here', 'Amine', 'Chakir', 'أمين', 'شاكر', 'student', '00000000-0000-0000-0000-000000001111'),
('10000000-0000-0000-0000-000000003112', 'S001002', '<EMAIL>', '$2b$10$example_hash_here', 'Salma', 'Benkirane', 'سلمى', 'بنكيران', 'student', '00000000-0000-0000-0000-000000001111'),
('10000000-0000-0000-0000-000000003121', 'S002001', '<EMAIL>', '$2b$10$example_hash_here', 'Yassine', 'Lahlou', 'ياسين', 'لحلو', 'student', '00000000-0000-0000-0000-000000001112');

-- إدراج رسائل تجريبية
INSERT INTO messages (id, sender_id, receiver_id, organization_id, subject, content, priority) VALUES 
('20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000011', '00000000-0000-0000-0000-000000000011', 'تعميم حول بداية السنة الدراسية', 'يشرفني أن أوجه إليكم هذا التعميم حول الاستعدادات لبداية السنة الدراسية الجديدة...', 'high'),
('20000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000001111', '10000000-0000-0000-0000-000000002111', '00000000-0000-0000-0000-000000001111', 'جدولة الحصص الدراسية', 'أستاذي الكريم، يرجى مراجعة الجدولة الجديدة للحصص الدراسية المرفقة...', 'normal');

-- إدراج إشعارات تجريبية
INSERT INTO notifications (user_id, title, title_ar, content, content_ar, type) VALUES 
('10000000-0000-0000-0000-000000002111', 'New Message Received', 'رسالة جديدة', 'You have received a new message from the school director', 'لقد تلقيت رسالة جديدة من مدير المؤسسة', 'message'),
('10000000-0000-0000-0000-000000003111', 'Grade Update', 'تحديث النقط', 'Your grades have been updated for Mathematics', 'تم تحديث نقطك في مادة الرياضيات', 'system');

-- إدراج وثائق تجريبية
INSERT INTO documents (uploaded_by, organization_id, title, title_ar, description, description_ar, file_path, file_type, file_size, category) VALUES 
('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Academic Year Guidelines', 'دليل السنة الدراسية', 'Guidelines for the new academic year', 'دليل للسنة الدراسية الجديدة', '/documents/academic_guidelines_2024.pdf', 'application/pdf', 2048000, 'guideline'),
('10000000-0000-0000-0000-000000001111', '00000000-0000-0000-0000-000000001111', 'School Rules and Regulations', 'النظام الداخلي للمؤسسة', 'Internal rules and regulations of the school', 'النظام الداخلي وقوانين المؤسسة', '/documents/school_rules_hassan2.pdf', 'application/pdf', 1024000, 'guideline');

-- إدراج لوحات متابعة أولية
INSERT INTO dashboards (organization_id, name, name_ar, config) VALUES 
('00000000-0000-0000-0000-000000000001', 'National Dashboard', 'لوحة المتابعة الوطنية', '{"widgets": ["student_count", "teacher_count", "school_count", "success_rate"]}'),
('00000000-0000-0000-0000-000000000011', 'Regional Dashboard - Casablanca', 'لوحة المتابعة الجهوية - الدار البيضاء', '{"widgets": ["regional_stats", "provincial_comparison", "performance_trends"]}'),
('00000000-0000-0000-0000-000000001111', 'School Dashboard - Hassan II', 'لوحة متابعة ثانوية الحسن الثاني', '{"widgets": ["student_attendance", "grade_distribution", "teacher_schedule"]}');
