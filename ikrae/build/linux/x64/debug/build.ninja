# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: runner
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/
# =============================================================================
# Object build statements for EXECUTABLE target ikrae


#############################################
# Order-only phony target for ikrae

build cmake_object_order_depends_target_ikrae: phony || flutter/flutter_assemble

build CMakeFiles/ikrae.dir/main.cc.o: CXX_COMPILER__ikrae_unscanned_Debug /home/<USER>/Desktop/ikrae/ikrae/linux/main.cc || cmake_object_order_depends_target_ikrae
  DEFINES = -DAPPLICATION_ID=\"com.example.ikrae\"
  DEP_FILE = CMakeFiles/ikrae.dir/main.cc.o.d
  FLAGS = -g -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/sysprof-6 -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/webp -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/cloudproviders -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = CMakeFiles/ikrae.dir
  OBJECT_FILE_DIR = CMakeFiles/ikrae.dir
  TARGET_COMPILE_PDB = CMakeFiles/ikrae.dir/
  TARGET_PDB = intermediates_do_not_run/ikrae.pdb

build CMakeFiles/ikrae.dir/my_application.cc.o: CXX_COMPILER__ikrae_unscanned_Debug /home/<USER>/Desktop/ikrae/ikrae/linux/my_application.cc || cmake_object_order_depends_target_ikrae
  DEFINES = -DAPPLICATION_ID=\"com.example.ikrae\"
  DEP_FILE = CMakeFiles/ikrae.dir/my_application.cc.o.d
  FLAGS = -g -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/sysprof-6 -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/webp -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/cloudproviders -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = CMakeFiles/ikrae.dir
  OBJECT_FILE_DIR = CMakeFiles/ikrae.dir
  TARGET_COMPILE_PDB = CMakeFiles/ikrae.dir/
  TARGET_PDB = intermediates_do_not_run/ikrae.pdb

build CMakeFiles/ikrae.dir/flutter/generated_plugin_registrant.cc.o: CXX_COMPILER__ikrae_unscanned_Debug /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/generated_plugin_registrant.cc || cmake_object_order_depends_target_ikrae
  DEFINES = -DAPPLICATION_ID=\"com.example.ikrae\"
  DEP_FILE = CMakeFiles/ikrae.dir/flutter/generated_plugin_registrant.cc.o.d
  FLAGS = -g -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /usr/include/sysprof-6 -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/fribidi -isystem /usr/include/cairo -isystem /usr/include/pixman-1 -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/webp -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/cloudproviders -isystem /usr/include/atk-1.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/x86_64-linux-gnu/dbus-1.0/include
  OBJECT_DIR = CMakeFiles/ikrae.dir
  OBJECT_FILE_DIR = CMakeFiles/ikrae.dir/flutter
  TARGET_COMPILE_PDB = CMakeFiles/ikrae.dir/
  TARGET_PDB = intermediates_do_not_run/ikrae.pdb


# =============================================================================
# Link build statements for EXECUTABLE target ikrae


#############################################
# Link the executable intermediates_do_not_run/ikrae

build intermediates_do_not_run/ikrae: CXX_EXECUTABLE_LINKER__ikrae_Debug CMakeFiles/ikrae.dir/main.cc.o CMakeFiles/ikrae.dir/my_application.cc.o CMakeFiles/ikrae.dir/flutter/generated_plugin_registrant.cc.o | /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/libflutter_linux_gtk.so /usr/lib/x86_64-linux-gnu/libgtk-3.so /usr/lib/x86_64-linux-gnu/libgdk-3.so /usr/lib/x86_64-linux-gnu/libz.so /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /usr/lib/x86_64-linux-gnu/libpango-1.0.so /usr/lib/x86_64-linux-gnu/libharfbuzz.so /usr/lib/x86_64-linux-gnu/libatk-1.0.so /usr/lib/x86_64-linux-gnu/libcairo-gobject.so /usr/lib/x86_64-linux-gnu/libcairo.so /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /usr/lib/x86_64-linux-gnu/libgio-2.0.so /usr/lib/x86_64-linux-gnu/libgobject-2.0.so /usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  DEP_FILE = CMakeFiles/ikrae.dir/link.d
  FLAGS = -g
  LINK_FLAGS = -Xlinker --dependency-file=CMakeFiles/ikrae.dir/link.d
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral:  -lflutter_linux_gtk  /usr/lib/x86_64-linux-gnu/libgtk-3.so  /usr/lib/x86_64-linux-gnu/libgdk-3.so  /usr/lib/x86_64-linux-gnu/libz.so  /usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /usr/lib/x86_64-linux-gnu/libpango-1.0.so  /usr/lib/x86_64-linux-gnu/libharfbuzz.so  /usr/lib/x86_64-linux-gnu/libatk-1.0.so  /usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /usr/lib/x86_64-linux-gnu/libcairo.so  /usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /usr/lib/x86_64-linux-gnu/libgio-2.0.so  /usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /usr/lib/x86_64-linux-gnu/libglib-2.0.so
  LINK_PATH = -L/home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral
  OBJECT_DIR = CMakeFiles/ikrae.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/ikrae.dir/
  TARGET_FILE = intermediates_do_not_run/ikrae
  TARGET_PDB = intermediates_do_not_run/ikrae.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Desktop/ikrae/ikrae/linux -B/home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Desktop/ikrae/ikrae/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for flutter_assemble

build flutter/flutter_assemble: phony flutter/CMakeFiles/flutter_assemble /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_


#############################################
# Utility command for edit_cache

build flutter/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/flutter && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build flutter/edit_cache: phony flutter/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build flutter/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/flutter && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Desktop/ikrae/ikrae/linux -B/home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build flutter/rebuild_cache: phony flutter/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build flutter/list_install_components: phony


#############################################
# Utility command for install

build flutter/CMakeFiles/install.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/flutter && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build flutter/install: phony flutter/CMakeFiles/install.util


#############################################
# Utility command for install/local

build flutter/CMakeFiles/install/local.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/flutter && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build flutter/install/local: phony flutter/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build flutter/CMakeFiles/install/strip.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/flutter && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build flutter/install/strip: phony flutter/CMakeFiles/install/strip.util


#############################################
# Phony custom command for flutter/CMakeFiles/flutter_assemble

build flutter/CMakeFiles/flutter_assemble | ${cmake_ninja_workdir}flutter/CMakeFiles/flutter_assemble: phony /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/flutter_linux.h


#############################################
# Custom command for /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/libflutter_linux_gtk.so

build /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_ | ${cmake_ninja_workdir}flutter/_phony_: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/flutter && /usr/bin/cmake -E env FLUTTER_ROOT=/opt/flutter PROJECT_DIR=/home/<USER>/Desktop/ikrae/ikrae DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=/home/<USER>/Desktop/ikrae/ikrae/.dart_tool/package_config.json FLUTTER_TARGET=/home/<USER>/Desktop/ikrae/ikrae/lib/main.dart /opt/flutter/packages/flutter_tools/bin/tool_backend.sh linux-x64 Debug
  DESC = Generating /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/libflutter_linux_gtk.so, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_engine.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_call.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_method_response.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_value.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/fl_view.h, /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/flutter_linux/flutter_linux.h, _phony_
  restat = 1

# =============================================================================
# Target aliases.

build flutter_assemble: phony flutter/flutter_assemble

build ikrae: phony intermediates_do_not_run/ikrae

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug

build all: phony intermediates_do_not_run/ikrae flutter/all

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/ikrae/ikrae/build/linux/x64/debug/flutter

build flutter/all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/Desktop/ikrae/ikrae/linux/CMakeLists.txt /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/CMakeLists.txt /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/generated_plugins.cmake /usr/share/cmake-3.31/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.31/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.31/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompilerSupport.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.31/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.31/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.31/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.31/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.31/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.31/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.31/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.31/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.31/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-CXX-CXXImportStd.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang.cmake /usr/share/cmake-3.31/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/GNU.cmake /usr/share/cmake-3.31/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.31/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.31/Modules/FindPackageMessage.cmake /usr/share/cmake-3.31/Modules/FindPkgConfig.cmake /usr/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/share/cmake-3.31/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake /usr/share/cmake-3.31/Modules/Linker/GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linker/Linux-GNU-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linker/Linux-GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-Clang-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.31/Modules/Platform/Linux.cmake /usr/share/cmake-3.31/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.6/CMakeCXXCompiler.cmake CMakeFiles/3.31.6/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/Desktop/ikrae/ikrae/linux/CMakeLists.txt /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/CMakeLists.txt /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Desktop/ikrae/ikrae/linux/flutter/generated_plugins.cmake /usr/share/cmake-3.31/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.31/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.31/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineCompilerSupport.cmake /usr/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.31/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.31/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.31/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.31/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.31/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.31/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.31/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.31/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.31/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-CXX-CXXImportStd.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake-3.31/Modules/Compiler/Clang.cmake /usr/share/cmake-3.31/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/GNU.cmake /usr/share/cmake-3.31/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.31/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.31/Modules/FindPackageMessage.cmake /usr/share/cmake-3.31/Modules/FindPkgConfig.cmake /usr/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/share/cmake-3.31/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake /usr/share/cmake-3.31/Modules/Linker/GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linker/Linux-GNU-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linker/Linux-GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-Clang-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.31/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.31/Modules/Platform/Linux.cmake /usr/share/cmake-3.31/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.6/CMakeCXXCompiler.cmake CMakeFiles/3.31.6/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
