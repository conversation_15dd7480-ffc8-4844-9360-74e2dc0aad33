import 'package:flutter/material.dart';
import 'dart:convert';
import '../services/auth_service.dart';
import '../models/message.dart';
import '../models/user.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  late TabController _tabController;
  
  List<Message> _receivedMessages = [];
  List<Message> _sentMessages = [];
  List<User> _contacts = [];
  
  bool _isLoading = true;
  bool _isLoadingContacts = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadMessages();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load received messages
      final receivedResponse = await _authService.authenticatedRequest(
        'GET',
        '/messages?type=received&limit=50',
      );

      if (receivedResponse.statusCode == 200) {
        final receivedData = json.decode(receivedResponse.body);
        setState(() {
          _receivedMessages = (receivedData['messages'] as List)
              .map((json) => Message.fromJson(json))
              .toList();
        });
      }

      // Load sent messages
      final sentResponse = await _authService.authenticatedRequest(
        'GET',
        '/messages?type=sent&limit=50',
      );

      if (sentResponse.statusCode == 200) {
        final sentData = json.decode(sentResponse.body);
        setState(() {
          _sentMessages = (sentData['messages'] as List)
              .map((json) => Message.fromJson(json))
              .toList();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الرسائل: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadContacts() async {
    setState(() {
      _isLoadingContacts = true;
    });

    try {
      final response = await _authService.authenticatedRequest(
        'GET',
        '/users/contacts/messageable',
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          _contacts = (data['contacts'] as List)
              .map((json) => User.fromJson(json))
              .toList();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل جهات الاتصال: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoadingContacts = false;
      });
    }
  }

  Widget _buildMessagesList(List<Message> messages, bool isReceived) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.message_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              isReceived ? 'لا توجد رسائل واردة' : 'لا توجد رسائل مرسلة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadMessages,
      child: ListView.builder(
        itemCount: messages.length,
        itemBuilder: (context, index) {
          final message = messages[index];
          return _buildMessageCard(message, isReceived);
        },
      ),
    );
  }

  Widget _buildMessageCard(Message message, bool isReceived) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getPriorityColor(message.priority),
          child: Icon(
            _getPriorityIcon(message.priority),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          message.subject,
          style: TextStyle(
            fontWeight: message.isRead ? FontWeight.normal : FontWeight.bold,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isReceived ? 'من: ${message.senderDisplayName}' : 'إلى: ${message.receiverDisplayName}',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(height: 2),
            Text(
              _formatDate(message.createdAt),
              style: const TextStyle(fontSize: 11),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!message.isRead && isReceived)
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
              ),
            const SizedBox(height: 4),
            Text(
              message.priorityDisplayName,
              style: TextStyle(
                fontSize: 10,
                color: _getPriorityColor(message.priority),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        onTap: () => _showMessageDetails(message),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'urgent':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'normal':
        return Colors.blue;
      case 'low':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData _getPriorityIcon(String priority) {
    switch (priority) {
      case 'urgent':
        return Icons.priority_high;
      case 'high':
        return Icons.arrow_upward;
      case 'normal':
        return Icons.remove;
      case 'low':
        return Icons.arrow_downward;
      default:
        return Icons.remove;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _showMessageDetails(Message message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(message.subject),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('من: ${message.senderDisplayName}'),
              const SizedBox(height: 8),
              Text('التاريخ: ${_formatDate(message.createdAt)}'),
              const SizedBox(height: 8),
              Text('الأولوية: ${message.priorityDisplayName}'),
              const Divider(),
              Text(message.content),
              if (message.attachments != null && message.attachments!.isNotEmpty) ...[
                const Divider(),
                const Text('المرفقات:'),
                ...message.attachments!.map((attachment) => 
                  ListTile(
                    dense: true,
                    leading: const Icon(Icons.attach_file),
                    title: Text(attachment.fileName),
                    subtitle: Text(attachment.fileSizeFormatted),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          if (!message.isRead)
            ElevatedButton(
              onPressed: () {
                // TODO: Mark as read
                Navigator.of(context).pop();
              },
              child: const Text('تمييز كمقروء'),
            ),
        ],
      ),
    );
  }

  void _showComposeDialog() {
    if (_contacts.isEmpty) {
      _loadContacts();
    }

    showDialog(
      context: context,
      builder: (context) => _ComposeMessageDialog(
        contacts: _contacts,
        isLoadingContacts: _isLoadingContacts,
        onSend: (receiverId, subject, content) async {
          try {
            final response = await _authService.authenticatedRequest(
              'POST',
              '/messages',
              body: {
                'receiver_id': receiverId,
                'subject': subject,
                'content': content,
                'priority': 'normal',
              },
            );

            if (response.statusCode == 201) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم إرسال الرسالة بنجاح')),
                );
                _loadMessages();
              }
              return true;
            } else {
              final error = json.decode(response.body);
              throw Exception(error['error_ar'] ?? error['error'] ?? 'فشل الإرسال');
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ في الإرسال: $e')),
              );
            }
            return false;
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الرسائل'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الواردة', icon: Icon(Icons.inbox)),
            Tab(text: 'المرسلة', icon: Icon(Icons.send)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMessages,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMessagesList(_receivedMessages, true),
          _buildMessagesList(_sentMessages, false),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showComposeDialog,
        child: const Icon(Icons.add),
      ),
    );
  }
}

class _ComposeMessageDialog extends StatefulWidget {
  final List<User> contacts;
  final bool isLoadingContacts;
  final Future<bool> Function(String receiverId, String subject, String content) onSend;

  const _ComposeMessageDialog({
    required this.contacts,
    required this.isLoadingContacts,
    required this.onSend,
  });

  @override
  State<_ComposeMessageDialog> createState() => _ComposeMessageDialogState();
}

class _ComposeMessageDialogState extends State<_ComposeMessageDialog> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _contentController = TextEditingController();
  
  User? _selectedContact;
  bool _isSending = false;

  @override
  void dispose() {
    _subjectController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('رسالة جديدة'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Contact selection
              DropdownButtonFormField<User>(
                value: _selectedContact,
                decoration: const InputDecoration(
                  labelText: 'المستقبل',
                  border: OutlineInputBorder(),
                ),
                items: widget.contacts.map((contact) {
                  return DropdownMenuItem(
                    value: contact,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(contact.displayName),
                        Text(
                          '${contact.roleDisplayName} - ${contact.organization?.displayName ?? ''}',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (contact) {
                  setState(() {
                    _selectedContact = contact;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار المستقبل';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Subject
              TextFormField(
                controller: _subjectController,
                decoration: const InputDecoration(
                  labelText: 'الموضوع',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال الموضوع';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Content
              TextFormField(
                controller: _contentController,
                decoration: const InputDecoration(
                  labelText: 'المحتوى',
                  border: OutlineInputBorder(),
                ),
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال المحتوى';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isSending ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isSending ? null : _sendMessage,
          child: _isSending
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إرسال'),
        ),
      ],
    );
  }

  Future<void> _sendMessage() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSending = true;
    });

    final success = await widget.onSend(
      _selectedContact!.id,
      _subjectController.text.trim(),
      _contentController.text.trim(),
    );

    if (success && mounted) {
      Navigator.of(context).pop();
    } else {
      setState(() {
        _isSending = false;
      });
    }
  }
}
