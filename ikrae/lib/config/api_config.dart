class ApiConfig {
  // Base URL for the API
  static const String baseUrl = 'http://localhost:3000/api/v1';
  
  // API endpoints
  static const String loginEndpoint = '/auth/login';
  static const String logoutEndpoint = '/auth/logout';
  static const String refreshEndpoint = '/auth/refresh';
  static const String profileEndpoint = '/auth/me';
  
  static const String messagesEndpoint = '/messages';
  static const String usersEndpoint = '/users';
  static const String organizationsEndpoint = '/organizations';
  static const String notificationsEndpoint = '/notifications';
  static const String documentsEndpoint = '/documents';
  
  // Request timeout
  static const Duration requestTimeout = Duration(seconds: 30);
  
  // File upload limits
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedFileTypes = [
    'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'
  ];
}
