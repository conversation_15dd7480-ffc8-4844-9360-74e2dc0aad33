import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'message.g.dart';

@JsonSerializable()
class Message {
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_id')
  final String senderId;
  @J<PERSON><PERSON><PERSON>(name: 'receiver_id')
  final String? receiverId;
  @J<PERSON><PERSON><PERSON>(name: 'organization_id')
  final String organizationId;
  final String subject;
  final String content;
  final String priority;
  final String status;
  @<PERSON>son<PERSON><PERSON>(name: 'is_read')
  final bool isRead;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'read_at')
  final DateTime? readAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_broadcast')
  final bool isBroadcast;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;

  // Sender information
  @JsonKey(name: 'sender_first_name')
  final String? senderFirstName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_last_name')
  final String? senderLastName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_first_name_ar')
  final String? senderFirstNameAr;
  @J<PERSON><PERSON><PERSON>(name: 'sender_last_name_ar')
  final String? senderLastNameAr;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_role')
  final String? senderRole;

  // Receiver information
  @JsonKey(name: 'receiver_first_name')
  final String? receiverFirstName;
  @JsonKey(name: 'receiver_last_name')
  final String? receiverLastName;
  @JsonKey(name: 'receiver_first_name_ar')
  final String? receiverFirstNameAr;
  @JsonKey(name: 'receiver_last_name_ar')
  final String? receiverLastNameAr;
  @JsonKey(name: 'receiver_role')
  final String? receiverRole;

  // Organization information
  @JsonKey(name: 'organization_name')
  final String? organizationName;
  @JsonKey(name: 'organization_name_ar')
  final String? organizationNameAr;

  // Attachments
  final List<MessageAttachment>? attachments;

  Message({
    required this.id,
    required this.senderId,
    this.receiverId,
    required this.organizationId,
    required this.subject,
    required this.content,
    required this.priority,
    required this.status,
    required this.isRead,
    this.readAt,
    required this.isBroadcast,
    required this.createdAt,
    this.updatedAt,
    this.senderFirstName,
    this.senderLastName,
    this.senderFirstNameAr,
    this.senderLastNameAr,
    this.senderRole,
    this.receiverFirstName,
    this.receiverLastName,
    this.receiverFirstNameAr,
    this.receiverLastNameAr,
    this.receiverRole,
    this.organizationName,
    this.organizationNameAr,
    this.attachments,
  });

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);
  Map<String, dynamic> toJson() => _$MessageToJson(this);

  String get senderDisplayName {
    if (senderFirstNameAr != null && senderLastNameAr != null) {
      return '$senderFirstNameAr $senderLastNameAr';
    }
    if (senderFirstName != null && senderLastName != null) {
      return '$senderFirstName $senderLastName';
    }
    return 'مرسل غير معروف';
  }

  String get receiverDisplayName {
    if (receiverFirstNameAr != null && receiverLastNameAr != null) {
      return '$receiverFirstNameAr $receiverLastNameAr';
    }
    if (receiverFirstName != null && receiverLastName != null) {
      return '$receiverFirstName $receiverLastName';
    }
    return 'مستقبل غير معروف';
  }

  String get priorityDisplayName {
    switch (priority) {
      case 'urgent':
        return 'عاجل';
      case 'high':
        return 'مهم';
      case 'normal':
        return 'عادي';
      case 'low':
        return 'منخفض';
      default:
        return priority;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case 'draft':
        return 'مسودة';
      case 'sent':
        return 'مرسل';
      case 'delivered':
        return 'تم التسليم';
      case 'read':
        return 'مقروء';
      default:
        return status;
    }
  }
}

@JsonSerializable()
class MessageAttachment {
  final String id;
  @JsonKey(name: 'message_id')
  final String messageId;
  @JsonKey(name: 'file_name')
  final String fileName;
  @JsonKey(name: 'file_path')
  final String? filePath;
  @JsonKey(name: 'file_type')
  final String fileType;
  @JsonKey(name: 'file_size')
  final int fileSize;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  MessageAttachment({
    required this.id,
    required this.messageId,
    required this.fileName,
    this.filePath,
    required this.fileType,
    required this.fileSize,
    this.createdAt,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) => _$MessageAttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$MessageAttachmentToJson(this);

  String get fileSizeFormatted {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}
