import 'user.dart';

class Message {
  final String id;
  final String senderId;
  final String? receiverId;
  final String organizationId;
  final String subject;
  final String content;
  final String priority;
  final String status;
  final bool isRead;
  final DateTime? readAt;
  final bool isBroadcast;
  final DateTime createdAt;
  final DateTime? updatedAt;

  // Sender information
  final String? senderFirstName;
  final String? senderLastName;
  final String? senderFirstNameAr;
  final String? senderLastNameAr;
  final String? senderRole;

  // Receiver information
  final String? receiverFirstName;
  final String? receiverLastName;
  final String? receiverFirstNameAr;
  final String? receiverLastNameAr;
  final String? receiverRole;

  // Organization information
  final String? organizationName;
  final String? organizationNameAr;

  // Attachments
  final List<MessageAttachment>? attachments;

  Message({
    required this.id,
    required this.senderId,
    this.receiverId,
    required this.organizationId,
    required this.subject,
    required this.content,
    required this.priority,
    required this.status,
    required this.isRead,
    this.readAt,
    required this.isBroadcast,
    required this.createdAt,
    this.updatedAt,
    this.senderFirstName,
    this.senderLastName,
    this.senderFirstNameAr,
    this.senderLastNameAr,
    this.senderRole,
    this.receiverFirstName,
    this.receiverLastName,
    this.receiverFirstNameAr,
    this.receiverLastNameAr,
    this.receiverRole,
    this.organizationName,
    this.organizationNameAr,
    this.attachments,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as String,
      senderId: json['sender_id'] as String,
      receiverId: json['receiver_id'] as String?,
      organizationId: json['organization_id'] as String,
      subject: json['subject'] as String,
      content: json['content'] as String,
      priority: json['priority'] as String,
      status: json['status'] as String,
      isRead: json['is_read'] as bool,
      readAt: json['read_at'] != null
          ? DateTime.parse(json['read_at'] as String)
          : null,
      isBroadcast: json['is_broadcast'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      senderFirstName: json['sender_first_name'] as String?,
      senderLastName: json['sender_last_name'] as String?,
      senderFirstNameAr: json['sender_first_name_ar'] as String?,
      senderLastNameAr: json['sender_last_name_ar'] as String?,
      senderRole: json['sender_role'] as String?,
      receiverFirstName: json['receiver_first_name'] as String?,
      receiverLastName: json['receiver_last_name'] as String?,
      receiverFirstNameAr: json['receiver_first_name_ar'] as String?,
      receiverLastNameAr: json['receiver_last_name_ar'] as String?,
      receiverRole: json['receiver_role'] as String?,
      organizationName: json['organization_name'] as String?,
      organizationNameAr: json['organization_name_ar'] as String?,
      attachments: json['attachments'] != null
          ? (json['attachments'] as List)
              .map((e) => MessageAttachment.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'organization_id': organizationId,
      'subject': subject,
      'content': content,
      'priority': priority,
      'status': status,
      'is_read': isRead,
      'read_at': readAt?.toIso8601String(),
      'is_broadcast': isBroadcast,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sender_first_name': senderFirstName,
      'sender_last_name': senderLastName,
      'sender_first_name_ar': senderFirstNameAr,
      'sender_last_name_ar': senderLastNameAr,
      'sender_role': senderRole,
      'receiver_first_name': receiverFirstName,
      'receiver_last_name': receiverLastName,
      'receiver_first_name_ar': receiverFirstNameAr,
      'receiver_last_name_ar': receiverLastNameAr,
      'receiver_role': receiverRole,
      'organization_name': organizationName,
      'organization_name_ar': organizationNameAr,
      'attachments': attachments?.map((e) => e.toJson()).toList(),
    };
  }

  String get senderDisplayName {
    if (senderFirstNameAr != null && senderLastNameAr != null) {
      return '$senderFirstNameAr $senderLastNameAr';
    }
    if (senderFirstName != null && senderLastName != null) {
      return '$senderFirstName $senderLastName';
    }
    return 'مرسل غير معروف';
  }

  String get receiverDisplayName {
    if (receiverFirstNameAr != null && receiverLastNameAr != null) {
      return '$receiverFirstNameAr $receiverLastNameAr';
    }
    if (receiverFirstName != null && receiverLastName != null) {
      return '$receiverFirstName $receiverLastName';
    }
    return 'مستقبل غير معروف';
  }

  String get priorityDisplayName {
    switch (priority) {
      case 'urgent':
        return 'عاجل';
      case 'high':
        return 'مهم';
      case 'normal':
        return 'عادي';
      case 'low':
        return 'منخفض';
      default:
        return priority;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case 'draft':
        return 'مسودة';
      case 'sent':
        return 'مرسل';
      case 'delivered':
        return 'تم التسليم';
      case 'read':
        return 'مقروء';
      default:
        return status;
    }
  }
}

class MessageAttachment {
  final String id;
  final String messageId;
  final String fileName;
  final String? filePath;
  final String fileType;
  final int fileSize;
  final DateTime? createdAt;

  MessageAttachment({
    required this.id,
    required this.messageId,
    required this.fileName,
    this.filePath,
    required this.fileType,
    required this.fileSize,
    this.createdAt,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) {
    return MessageAttachment(
      id: json['id'] as String,
      messageId: json['message_id'] as String,
      fileName: json['file_name'] as String,
      filePath: json['file_path'] as String?,
      fileType: json['file_type'] as String,
      fileSize: json['file_size'] as int,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message_id': messageId,
      'file_name': fileName,
      'file_path': filePath,
      'file_type': fileType,
      'file_size': fileSize,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  String get fileSizeFormatted {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}
