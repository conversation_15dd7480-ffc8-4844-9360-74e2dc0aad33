import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final String id;
  @<PERSON>son<PERSON><PERSON>(name: 'massar_id')
  final String? massarId;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String firstName;
  @<PERSON>son<PERSON>ey(name: 'last_name')
  final String lastName;
  @<PERSON>son<PERSON>ey(name: 'first_name_ar')
  final String? firstNameAr;
  @<PERSON>sonKey(name: 'last_name_ar')
  final String? lastNameAr;
  final String role;
  final String? phone;
  final Organization? organization;
  @<PERSON>sonKey(name: 'profile_picture')
  final String? profilePicture;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_login')
  final DateTime? lastLogin;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime? createdAt;

  User({
    required this.id,
    this.massarId,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.firstNameAr,
    this.lastNameAr,
    required this.role,
    this.phone,
    this.organization,
    this.profilePicture,
    this.lastLogin,
    this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  String get displayName {
    if (firstNameAr != null && lastNameAr != null) {
      return '$firstNameAr $lastNameAr';
    }
    return '$firstName $lastName';
  }

  String get roleDisplayName {
    switch (role) {
      case 'national_admin':
        return 'مسؤول وطني';
      case 'regional_director':
        return 'مدير جهة';
      case 'provincial_director':
        return 'مدير إقليم';
      case 'school_director':
        return 'مدير مؤسسة';
      case 'teacher':
        return 'أستاذ';
      case 'student':
        return 'تلميذ';
      default:
        return role;
    }
  }
}

@JsonSerializable()
class Organization {
  final String id;
  final String name;
  @JsonKey(name: 'name_ar')
  final String? nameAr;
  final String code;
  final String type;
  final String? address;
  final String? phone;
  final String? email;

  Organization({
    required this.id,
    required this.name,
    this.nameAr,
    required this.code,
    required this.type,
    this.address,
    this.phone,
    this.email,
  });

  factory Organization.fromJson(Map<String, dynamic> json) => _$OrganizationFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationToJson(this);

  String get displayName => nameAr ?? name;

  String get typeDisplayName {
    switch (type) {
      case 'ministry':
        return 'وزارة';
      case 'region':
        return 'جهة';
      case 'province':
        return 'إقليم';
      case 'school':
        return 'مؤسسة تعليمية';
      default:
        return type;
    }
  }
}
