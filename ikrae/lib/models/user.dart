class User {
  final String id;
  final String? massarId;
  final String email;
  final String firstName;
  final String lastName;
  final String? firstNameAr;
  final String? lastNameAr;
  final String role;
  final String? phone;
  final Organization? organization;
  final String? profilePicture;
  final DateTime? lastLogin;
  final DateTime? createdAt;

  User({
    required this.id,
    this.massarId,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.firstNameAr,
    this.lastNameAr,
    required this.role,
    this.phone,
    this.organization,
    this.profilePicture,
    this.lastLogin,
    this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      massarId: json['massar_id'] as String?,
      email: json['email'] as String,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      firstNameAr: json['first_name_ar'] as String?,
      lastNameAr: json['last_name_ar'] as String?,
      role: json['role'] as String,
      phone: json['phone'] as String?,
      organization: json['organization'] != null
          ? Organization.fromJson(json['organization'] as Map<String, dynamic>)
          : null,
      profilePicture: json['profile_picture'] as String?,
      lastLogin: json['last_login'] != null
          ? DateTime.parse(json['last_login'] as String)
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'massar_id': massarId,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'first_name_ar': firstNameAr,
      'last_name_ar': lastNameAr,
      'role': role,
      'phone': phone,
      'organization': organization?.toJson(),
      'profile_picture': profilePicture,
      'last_login': lastLogin?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
    };
  }

  String get displayName {
    if (firstNameAr != null && lastNameAr != null) {
      return '$firstNameAr $lastNameAr';
    }
    return '$firstName $lastName';
  }

  String get roleDisplayName {
    switch (role) {
      case 'national_admin':
        return 'مسؤول وطني';
      case 'regional_director':
        return 'مدير جهة';
      case 'provincial_director':
        return 'مدير إقليم';
      case 'school_director':
        return 'مدير مؤسسة';
      case 'teacher':
        return 'أستاذ';
      case 'student':
        return 'تلميذ';
      default:
        return role;
    }
  }
}

class Organization {
  final String id;
  final String name;
  final String? nameAr;
  final String code;
  final String type;
  final String? address;
  final String? phone;
  final String? email;

  Organization({
    required this.id,
    required this.name,
    this.nameAr,
    required this.code,
    required this.type,
    this.address,
    this.phone,
    this.email,
  });

  factory Organization.fromJson(Map<String, dynamic> json) {
    return Organization(
      id: json['id'] as String,
      name: json['name'] as String,
      nameAr: json['name_ar'] as String?,
      code: json['code'] as String,
      type: json['type'] as String,
      address: json['address'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'code': code,
      'type': type,
      'address': address,
      'phone': phone,
      'email': email,
    };
  }

  String get displayName => nameAr ?? name;

  String get typeDisplayName {
    switch (type) {
      case 'ministry':
        return 'وزارة';
      case 'region':
        return 'جهة';
      case 'province':
        return 'إقليم';
      case 'school':
        return 'مؤسسة تعليمية';
      default:
        return type;
    }
  }
}
