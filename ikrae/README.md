# ikrae

# إقرأ - نظام التواصل التعليمي المغربي
# Ikrae - Moroccan Educational Communication System

<div align="center">
  <img src="assets/logo.png" alt="Ikrae Logo" width="120" height="120">

  [![Flutter](https://img.shields.io/badge/Flutter-3.24.5-blue.svg)](https://flutter.dev/)
  [![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://postgresql.org/)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
</div>

## 📖 نظرة عامة | Overview

**إقرأ** هو نظام تواصل تعليمي مخصص للمؤسسات التعليمية في المغرب، يهدف إلى تسهيل التواصل بين مختلف المستويات الإدارية والتعليمية وفقاً للتسلسل الهرمي للنظام التعليمي المغربي.

**Ikrae** is an educational communication system designed for Moroccan educational institutions, aimed at facilitating communication between different administrative and educational levels according to the hierarchical structure of the Moroccan education system.

## 🎯 الأهداف | Objectives

- تحسين التواصل بين المستويات الإدارية المختلفة
- تسريع تبادل المعلومات والوثائق الرسمية
- توفير نظام متابعة شامل للمؤشرات التعليمية
- دعم اللغتين العربية والفرنسية
- التكامل مع أنظمة "مسار" و"مسير" الحالية

## 🏗️ التسلسل الهرمي | Hierarchical Structure

```
مسؤول وطني (وزارة التربية الوطنية)
    ↓
مديرو الجهات (الأكاديميات الجهوية)
    ↓
مديرو الأقاليم (المديريات الإقليمية)
    ↓
مديرو المؤسسات التعليمية
    ↓
الأساتذة
    ↓
التلاميذ
```

## ✨ الميزات الرئيسية | Key Features

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن باستخدام رقم مسار أو البريد الرسمي
- صلاحيات هرمية حسب المستوى الإداري
- نظام JWT للأمان

### 💬 نظام الرسائل
- إرسال واستقبال الرسائل بين المستويات
- رسائل جماعية للإعلانات الرسمية
- مرفقات الملفات والوثائق
- أولويات الرسائل (عادي، مهم، عاجل)

### 🔔 الإشعارات الفورية
- إشعارات فورية للرسائل الجديدة
- إشعارات داخل التطبيق
- دعم Push Notifications

### 📊 لوحات المتابعة
- مؤشرات وطنية للمسؤول الوطني
- مؤشرات جهوية لمديري الجهات
- مؤشرات إقليمية لمديري الأقاليم
- إحصائيات المؤسسة لمديري المدارس

### 📁 إدارة الوثائق
- رفع وتحميل الوثائق الرسمية
- تصنيف الوثائق حسب النوع
- صلاحيات الوصول المتدرجة
- البحث في الوثائق

## 🛠️ التقنيات المستخدمة | Technologies Used

### Frontend (Mobile App)
- **Flutter 3.24.5** - إطار تطوير التطبيقات المحمولة
- **Dart** - لغة البرمجة
- **Provider** - إدارة الحالة
- **HTTP** - طلبات API
- **SharedPreferences** - التخزين المحلي

### Backend (API Server)
- **Node.js 18+** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **PostgreSQL 15+** - قاعدة البيانات الرئيسية
- **Redis** - التخزين المؤقت والجلسات
- **JWT** - المصادقة والتوكينات
- **Socket.io** - التواصل الفوري

### DevOps & Infrastructure
- **Docker** - الحاويات
- **Nginx** - خادم الويب وموازن الأحمال
- **PM2** - إدارة العمليات
- **Winston** - نظام السجلات

## 🚀 التثبيت والتشغيل | Installation & Setup

### متطلبات النظام | System Requirements

- **Node.js** 18.0.0 أو أحدث
- **Flutter** 3.24.5 أو أحدث
- **PostgreSQL** 15 أو أحدث
- **Redis** 6.0 أو أحدث

### 1. إعداد قاعدة البيانات | Database Setup

```bash
# إنشاء قاعدة البيانات
createdb ikrae_db

# تشغيل ملف المخطط
psql -d ikrae_db -f database/schema.sql

# إدراج البيانات الأولية
psql -d ikrae_db -f database/seed_data.sql
```

### 2. إعداد الخلفية | Backend Setup

```bash
# الانتقال إلى مجلد الخلفية
cd backend

# تثبيت التبعيات
npm install

# نسخ ملف البيئة
cp .env.example .env

# تحرير متغيرات البيئة
nano .env

# تشغيل الخادم في وضع التطوير
npm run dev
```

### 3. إعداد التطبيق | Flutter App Setup

```bash
# الانتقال إلى مجلد التطبيق
cd ../

# تثبيت تبعيات Flutter
flutter pub get

# تشغيل التطبيق
flutter run
```

## 📱 لقطات الشاشة | Screenshots

<div align="center">
  <img src="docs/screenshots/login.png" alt="Login Screen" width="250">
  <img src="docs/screenshots/home.png" alt="Home Screen" width="250">
  <img src="docs/screenshots/messages.png" alt="Messages Screen" width="250">
</div>

## 🧪 الاختبار | Testing

### اختبار الخلفية | Backend Testing
```bash
cd backend
npm test
```

### اختبار التطبيق | Flutter Testing
```bash
flutter test
```

## 📚 التوثيق | Documentation

- [خطة التطوير التدريجية](docs/development_roadmap.md)
- [دليل API](docs/api_documentation.md)
- [دليل المستخدم](docs/user_guide.md)
- [دليل المطور](docs/developer_guide.md)

## 🤝 المساهمة | Contributing

نرحب بمساهماتكم في تطوير هذا المشروع! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) للمزيد من التفاصيل.

We welcome contributions to this project! Please read our [Contributing Guide](CONTRIBUTING.md) for more details.

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 الفريق | Team

- **المطور الرئيسي** | Lead Developer: [اسمك هنا]
- **مصمم UI/UX** | UI/UX Designer: [اسم المصمم]
- **مهندس DevOps** | DevOps Engineer: [اسم المهندس]

## 📞 الدعم | Support

للحصول على الدعم أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 🐛 تقارير الأخطاء: [GitHub Issues](https://github.com/your-repo/ikrae/issues)
- 📖 الوثائق: [Wiki](https://github.com/your-repo/ikrae/wiki)

## 🔄 حالة التطوير | Development Status

- ✅ **MVP (النموذج الأولي)** - مكتمل
- 🔄 **المرحلة الثانية** - قيد التطوير
- ⏳ **المرحلة الثالثة** - مخطط لها
- ⏳ **النشر الكامل** - مخطط له

---

<div align="center">
  <p>صُنع بـ ❤️ للنظام التعليمي المغربي</p>
  <p>Made with ❤️ for the Moroccan Education System</p>
</div>
