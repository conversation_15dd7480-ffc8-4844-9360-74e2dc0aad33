{"name": "ikrae-backend", "version": "1.0.0", "description": "Backend API for Moroccan Educational Communication System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "knex migrate:latest", "seed": "knex seed:run"}, "keywords": ["education", "morocco", "communication", "api"], "author": "Ikrae Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "knex": "^3.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "nodemailer": "^6.9.7", "joi": "^17.11.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}