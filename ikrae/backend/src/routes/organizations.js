const express = require('express');
const { query } = require('../config/database');
const { logger } = require('../utils/logger');
const { authenticateToken, authorize } = require('../middleware/auth');

const router = express.Router();
router.use(authenticateToken);

// GET /api/v1/organizations - Get organizations hierarchy
router.get('/', async (req, res) => {
  try {
    const result = await query(`
      WITH RECURSIVE org_tree AS (
        SELECT id, name, name_ar, code, type, parent_id, 0 as level
        FROM organizations 
        WHERE parent_id IS NULL
        
        UNION ALL
        
        SELECT o.id, o.name, o.name_ar, o.code, o.type, o.parent_id, ot.level + 1
        FROM organizations o
        JOIN org_tree ot ON o.parent_id = ot.id
      )
      SELECT * FROM org_tree ORDER BY level, type, name
    `);

    res.json({
      organizations: result.rows
    });

  } catch (error) {
    logger.error('Get organizations error:', error);
    res.status(500).json({
      error: 'Failed to get organizations',
      error_ar: 'فشل في الحصول على المؤسسات'
    });
  }
});

module.exports = router;
