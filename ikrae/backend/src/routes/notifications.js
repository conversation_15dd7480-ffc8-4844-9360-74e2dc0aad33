const express = require('express');
const { query } = require('../config/database');
const { logger } = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
router.use(authenticateToken);

// GET /api/v1/notifications - Get user notifications
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20, unread_only = false } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE user_id = $1';
    const queryParams = [userId];

    if (unread_only === 'true') {
      whereClause += ' AND is_read = false';
    }

    const result = await query(`
      SELECT * FROM notifications
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `, [...queryParams, limit, offset]);

    const countResult = await query(`
      SELECT COUNT(*) as total FROM notifications ${whereClause}
    `, queryParams);

    const total = parseInt(countResult.rows[0].total);

    res.json({
      notifications: result.rows,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });

  } catch (error) {
    logger.error('Get notifications error:', error);
    res.status(500).json({
      error: 'Failed to get notifications',
      error_ar: 'فشل في الحصول على الإشعارات'
    });
  }
});

// PUT /api/v1/notifications/:id/read - Mark notification as read
router.put('/:id/read', async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user.id;

    const result = await query(`
      UPDATE notifications 
      SET is_read = true 
      WHERE id = $1 AND user_id = $2
      RETURNING id, is_read
    `, [notificationId, userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Notification not found',
        error_ar: 'الإشعار غير موجود'
      });
    }

    res.json({
      message: 'Notification marked as read',
      message_ar: 'تم تمييز الإشعار كمقروء'
    });

  } catch (error) {
    logger.error('Mark notification as read error:', error);
    res.status(500).json({
      error: 'Failed to mark notification as read',
      error_ar: 'فشل في تمييز الإشعار كمقروء'
    });
  }
});

module.exports = router;
