const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Joi = require('joi');
const { query, getUserWithHierarchy } = require('../config/database');
const { logger } = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required()
});

const registerSchema = Joi.object({
  massar_id: Joi.string().optional(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  first_name: Joi.string().min(2).max(100).required(),
  last_name: Joi.string().min(2).max(100).required(),
  first_name_ar: Joi.string().min(2).max(100).optional(),
  last_name_ar: Joi.string().min(2).max(100).optional(),
  phone: Joi.string().pattern(/^(\+212|0)[5-7][0-9]{8}$/).optional(),
  role: Joi.string().valid('national_admin', 'regional_director', 'provincial_director', 'school_director', 'teacher', 'student').required(),
  organization_id: Joi.string().uuid().required()
});

// Generate JWT tokens
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );

  const refreshToken = jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );

  return { accessToken, refreshToken };
};

// POST /api/v1/auth/login
router.post('/login', async (req, res) => {
  try {
    // Validate input
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        error_ar: 'خطأ في التحقق',
        details: error.details[0].message
      });
    }

    const { email, password } = value;

    // Find user by email or massar_id
    const userResult = await query(`
      SELECT u.*, o.name as organization_name, o.name_ar as organization_name_ar, o.type as organization_type
      FROM users u
      JOIN organizations o ON u.organization_id = o.id
      WHERE u.email = $1 OR u.massar_id = $1
    `, [email]);

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        error: 'Invalid credentials',
        error_ar: 'بيانات اعتماد غير صحيحة'
      });
    }

    const user = userResult.rows[0];

    // Check if account is active
    if (!user.is_active) {
      return res.status(401).json({
        error: 'Account is deactivated',
        error_ar: 'الحساب معطل'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid credentials',
        error_ar: 'بيانات اعتماد غير صحيحة'
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Update last login
    await query('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1', [user.id]);

    // Store session
    await query(`
      INSERT INTO user_sessions (user_id, token_hash, device_info, ip_address, expires_at)
      VALUES ($1, $2, $3, $4, $5)
    `, [
      user.id,
      await bcrypt.hash(refreshToken, 10),
      JSON.stringify({ userAgent: req.get('User-Agent') }),
      req.ip,
      new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    ]);

    // Remove sensitive data
    delete user.password_hash;

    logger.info(`User logged in: ${user.email}`);

    res.json({
      message: 'Login successful',
      message_ar: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        massar_id: user.massar_id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        first_name_ar: user.first_name_ar,
        last_name_ar: user.last_name_ar,
        role: user.role,
        organization: {
          id: user.organization_id,
          name: user.organization_name,
          name_ar: user.organization_name_ar,
          type: user.organization_type
        }
      },
      tokens: {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: process.env.JWT_EXPIRES_IN || '24h'
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      error_ar: 'فشل تسجيل الدخول'
    });
  }
});

// POST /api/v1/auth/register
router.post('/register', async (req, res) => {
  try {
    // Validate input
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        error_ar: 'خطأ في التحقق',
        details: error.details[0].message
      });
    }

    const userData = value;

    // Check if email already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [userData.email]);
    if (existingUser.rows.length > 0) {
      return res.status(409).json({
        error: 'Email already registered',
        error_ar: 'البريد الإلكتروني مسجل مسبقاً'
      });
    }

    // Check if massar_id already exists (if provided)
    if (userData.massar_id) {
      const existingMassar = await query('SELECT id FROM users WHERE massar_id = $1', [userData.massar_id]);
      if (existingMassar.rows.length > 0) {
        return res.status(409).json({
          error: 'Massar ID already registered',
          error_ar: 'رقم مسار مسجل مسبقاً'
        });
      }
    }

    // Verify organization exists
    const orgResult = await query('SELECT id FROM organizations WHERE id = $1', [userData.organization_id]);
    if (orgResult.rows.length === 0) {
      return res.status(400).json({
        error: 'Invalid organization',
        error_ar: 'مؤسسة غير صحيحة'
      });
    }

    // Hash password
    const passwordHash = await bcrypt.hash(userData.password, parseInt(process.env.BCRYPT_ROUNDS) || 12);

    // Insert new user
    const insertResult = await query(`
      INSERT INTO users (massar_id, email, password_hash, first_name, last_name, first_name_ar, last_name_ar, phone, role, organization_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING id, email, first_name, last_name, role, organization_id, created_at
    `, [
      userData.massar_id,
      userData.email,
      passwordHash,
      userData.first_name,
      userData.last_name,
      userData.first_name_ar,
      userData.last_name_ar,
      userData.phone,
      userData.role,
      userData.organization_id
    ]);

    const newUser = insertResult.rows[0];

    logger.info(`New user registered: ${newUser.email}`);

    res.status(201).json({
      message: 'Registration successful',
      message_ar: 'تم التسجيل بنجاح',
      user: newUser
    });

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      error_ar: 'فشل التسجيل'
    });
  }
});

// POST /api/v1/auth/refresh
router.post('/refresh', async (req, res) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(401).json({
        error: 'Refresh token required',
        error_ar: 'رمز التحديث مطلوب'
      });
    }

    // Verify refresh token
    const decoded = jwt.verify(refresh_token, process.env.JWT_SECRET);

    // Check if session exists
    const sessionResult = await query(`
      SELECT us.*, u.is_active
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      WHERE us.user_id = $1 AND us.expires_at > CURRENT_TIMESTAMP
    `, [decoded.userId]);

    if (sessionResult.rows.length === 0) {
      return res.status(401).json({
        error: 'Invalid refresh token',
        error_ar: 'رمز تحديث غير صالح'
      });
    }

    const session = sessionResult.rows[0];

    if (!session.is_active) {
      return res.status(401).json({
        error: 'Account is deactivated',
        error_ar: 'الحساب معطل'
      });
    }

    // Generate new tokens
    const { accessToken, refreshToken } = generateTokens(decoded.userId);

    // Update session with new refresh token
    await query(`
      UPDATE user_sessions 
      SET token_hash = $1, expires_at = $2
      WHERE user_id = $3
    `, [
      await bcrypt.hash(refreshToken, 10),
      new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      decoded.userId
    ]);

    res.json({
      message: 'Token refreshed successfully',
      message_ar: 'تم تحديث الرمز بنجاح',
      tokens: {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: process.env.JWT_EXPIRES_IN || '24h'
      }
    });

  } catch (error) {
    logger.error('Token refresh error:', error);
    res.status(401).json({
      error: 'Token refresh failed',
      error_ar: 'فشل تحديث الرمز'
    });
  }
});

// POST /api/v1/auth/logout
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // Delete user sessions
    await query('DELETE FROM user_sessions WHERE user_id = $1', [req.user.id]);

    logger.info(`User logged out: ${req.user.email}`);

    res.json({
      message: 'Logout successful',
      message_ar: 'تم تسجيل الخروج بنجاح'
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout failed',
      error_ar: 'فشل تسجيل الخروج'
    });
  }
});

// GET /api/v1/auth/me
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const userWithHierarchy = await getUserWithHierarchy(req.user.id);
    
    if (!userWithHierarchy) {
      return res.status(404).json({
        error: 'User not found',
        error_ar: 'المستخدم غير موجود'
      });
    }

    // Remove sensitive data
    delete userWithHierarchy.password_hash;

    res.json({
      user: userWithHierarchy
    });

  } catch (error) {
    logger.error('Get user profile error:', error);
    res.status(500).json({
      error: 'Failed to get user profile',
      error_ar: 'فشل في الحصول على ملف المستخدم'
    });
  }
});

module.exports = router;
