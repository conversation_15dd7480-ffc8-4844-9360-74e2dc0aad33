const express = require('express');
const { query, getUsersByHierarchy } = require('../config/database');
const { logger } = require('../utils/logger');
const { authenticateToken, authorize } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// GET /api/v1/users - Get users (with hierarchy filtering)
router.get('/', async (req, res) => {
  try {
    const { role, organization_id, search } = req.query;
    const userId = req.user.id;

    let queryText = `
      SELECT 
        u.id, u.massar_id, u.email, u.first_name, u.last_name, 
        u.first_name_ar, u.last_name_ar, u.role, u.phone, u.is_active,
        o.name as organization_name, o.name_ar as organization_name_ar, o.type as organization_type
      FROM users u
      JOIN organizations o ON u.organization_id = o.id
      WHERE u.is_active = true
    `;
    
    const queryParams = [];

    // Filter by role if specified
    if (role) {
      queryParams.push(role);
      queryText += ` AND u.role = $${queryParams.length}`;
    }

    // Filter by organization if specified
    if (organization_id) {
      queryParams.push(organization_id);
      queryText += ` AND u.organization_id = $${queryParams.length}`;
    }

    // Search functionality
    if (search) {
      queryParams.push(`%${search}%`);
      queryText += ` AND (
        u.first_name ILIKE $${queryParams.length} OR 
        u.last_name ILIKE $${queryParams.length} OR 
        u.first_name_ar ILIKE $${queryParams.length} OR 
        u.last_name_ar ILIKE $${queryParams.length} OR
        u.email ILIKE $${queryParams.length} OR
        u.massar_id ILIKE $${queryParams.length}
      )`;
    }

    // Only show users within the same hierarchy
    const userOrgResult = await query('SELECT organization_id FROM users WHERE id = $1', [userId]);
    const userOrgId = userOrgResult.rows[0].organization_id;
    
    // Get users in hierarchy
    const hierarchyUsers = await getUsersByHierarchy(userOrgId, 'down');
    const hierarchyUserIds = hierarchyUsers.map(u => u.id);
    
    if (hierarchyUserIds.length > 0) {
      queryText += ` AND u.id = ANY($${queryParams.length + 1})`;
      queryParams.push(hierarchyUserIds);
    }

    queryText += ' ORDER BY o.type, u.role, u.last_name';

    const result = await query(queryText, queryParams);

    res.json({
      users: result.rows
    });

  } catch (error) {
    logger.error('Get users error:', error);
    res.status(500).json({
      error: 'Failed to get users',
      error_ar: 'فشل في الحصول على المستخدمين'
    });
  }
});

// GET /api/v1/users/:id - Get specific user
router.get('/:id', async (req, res) => {
  try {
    const targetUserId = req.params.id;
    const currentUserId = req.user.id;

    const userResult = await query(`
      SELECT 
        u.id, u.massar_id, u.email, u.first_name, u.last_name, 
        u.first_name_ar, u.last_name_ar, u.role, u.phone, u.is_active,
        u.last_login, u.created_at,
        o.name as organization_name, o.name_ar as organization_name_ar, 
        o.type as organization_type, o.address as organization_address
      FROM users u
      JOIN organizations o ON u.organization_id = o.id
      WHERE u.id = $1
    `, [targetUserId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        error: 'User not found',
        error_ar: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    // Check if current user can view this user (same hierarchy)
    const currentUserOrgResult = await query('SELECT organization_id FROM users WHERE id = $1', [currentUserId]);
    const currentUserOrgId = currentUserOrgResult.rows[0].organization_id;
    
    const hierarchyUsers = await getUsersByHierarchy(currentUserOrgId, 'down');
    const canView = hierarchyUsers.some(u => u.id === targetUserId) || targetUserId === currentUserId;

    if (!canView) {
      return res.status(403).json({
        error: 'Access denied',
        error_ar: 'الوصول مرفوض'
      });
    }

    res.json({
      user: user
    });

  } catch (error) {
    logger.error('Get user error:', error);
    res.status(500).json({
      error: 'Failed to get user',
      error_ar: 'فشل في الحصول على المستخدم'
    });
  }
});

// GET /api/v1/users/contacts/messageable - Get users that current user can message
router.get('/contacts/messageable', async (req, res) => {
  try {
    const userId = req.user.id;
    const { search, role } = req.query;

    // Get user's organization
    const userOrgResult = await query('SELECT organization_id FROM users WHERE id = $1', [userId]);
    const userOrgId = userOrgResult.rows[0].organization_id;

    // Get users in both directions of hierarchy (up and down)
    const downHierarchy = await getUsersByHierarchy(userOrgId, 'down');
    const upHierarchy = await getUsersByHierarchy(userOrgId, 'up');
    
    // Combine and deduplicate
    const allUsers = [...downHierarchy, ...upHierarchy];
    const uniqueUsers = allUsers.filter((user, index, self) => 
      index === self.findIndex(u => u.id === user.id) && user.id !== userId
    );

    let filteredUsers = uniqueUsers;

    // Filter by role if specified
    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }

    // Search functionality
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.first_name.toLowerCase().includes(searchLower) ||
        user.last_name.toLowerCase().includes(searchLower) ||
        (user.first_name_ar && user.first_name_ar.includes(search)) ||
        (user.last_name_ar && user.last_name_ar.includes(search)) ||
        user.email.toLowerCase().includes(searchLower) ||
        (user.massar_id && user.massar_id.toLowerCase().includes(searchLower))
      );
    }

    // Sort by organization type and role
    filteredUsers.sort((a, b) => {
      const typeOrder = { ministry: 1, region: 2, province: 3, school: 4 };
      const roleOrder = { 
        national_admin: 1, regional_director: 2, provincial_director: 3, 
        school_director: 4, teacher: 5, student: 6 
      };
      
      if (typeOrder[a.organization_type] !== typeOrder[b.organization_type]) {
        return typeOrder[a.organization_type] - typeOrder[b.organization_type];
      }
      
      if (roleOrder[a.role] !== roleOrder[b.role]) {
        return roleOrder[a.role] - roleOrder[b.role];
      }
      
      return a.last_name.localeCompare(b.last_name);
    });

    res.json({
      contacts: filteredUsers.map(user => ({
        id: user.id,
        massar_id: user.massar_id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        first_name_ar: user.first_name_ar,
        last_name_ar: user.last_name_ar,
        role: user.role,
        organization_name: user.organization_name,
        organization_name_ar: user.organization_name_ar,
        organization_type: user.organization_type
      }))
    });

  } catch (error) {
    logger.error('Get messageable contacts error:', error);
    res.status(500).json({
      error: 'Failed to get contacts',
      error_ar: 'فشل في الحصول على جهات الاتصال'
    });
  }
});

module.exports = router;
