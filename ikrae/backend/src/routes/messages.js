const express = require('express');
const Joi = require('joi');
const { query, transaction } = require('../config/database');
const { logger } = require('../utils/logger');
const { authenticateToken, checkMessagingPermission } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const sendMessageSchema = Joi.object({
  receiver_id: Joi.string().uuid().required(),
  subject: Joi.string().min(1).max(255).required(),
  content: Joi.string().min(1).required(),
  priority: Joi.string().valid('low', 'normal', 'high', 'urgent').default('normal')
});

const broadcastMessageSchema = Joi.object({
  organization_id: Joi.string().uuid().required(),
  roles: Joi.array().items(Joi.string().valid('teacher', 'student', 'school_director')).min(1).required(),
  subject: Joi.string().min(1).max(255).required(),
  content: Joi.string().min(1).required(),
  priority: Joi.string().valid('low', 'normal', 'high', 'urgent').default('normal')
});

// GET /api/v1/messages - Get user's messages
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20, type = 'received', status } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let queryParams = [userId, limit, offset];

    if (type === 'sent') {
      whereClause = 'WHERE m.sender_id = $1';
    } else {
      whereClause = 'WHERE m.receiver_id = $1';
    }

    if (status) {
      whereClause += ` AND m.status = $${queryParams.length + 1}`;
      queryParams.push(status);
    }

    const messagesResult = await query(`
      SELECT 
        m.*,
        sender.first_name as sender_first_name,
        sender.last_name as sender_last_name,
        sender.first_name_ar as sender_first_name_ar,
        sender.last_name_ar as sender_last_name_ar,
        sender.role as sender_role,
        receiver.first_name as receiver_first_name,
        receiver.last_name as receiver_last_name,
        receiver.first_name_ar as receiver_first_name_ar,
        receiver.last_name_ar as receiver_last_name_ar,
        receiver.role as receiver_role,
        org.name as organization_name,
        org.name_ar as organization_name_ar,
        (
          SELECT json_agg(
            json_build_object(
              'id', ma.id,
              'file_name', ma.file_name,
              'file_type', ma.file_type,
              'file_size', ma.file_size
            )
          )
          FROM message_attachments ma
          WHERE ma.message_id = m.id
        ) as attachments
      FROM messages m
      LEFT JOIN users sender ON m.sender_id = sender.id
      LEFT JOIN users receiver ON m.receiver_id = receiver.id
      LEFT JOIN organizations org ON m.organization_id = org.id
      ${whereClause}
      ORDER BY m.created_at DESC
      LIMIT $2 OFFSET $3
    `, queryParams);

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM messages m
      ${whereClause.replace(/\$\d+/g, (match, offset) => {
        const paramIndex = parseInt(match.substring(1));
        return paramIndex <= queryParams.length - 2 ? match : '';
      })}
    `, queryParams.slice(0, -2));

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      messages: messagesResult.rows,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });

  } catch (error) {
    logger.error('Get messages error:', error);
    res.status(500).json({
      error: 'Failed to get messages',
      error_ar: 'فشل في الحصول على الرسائل'
    });
  }
});

// POST /api/v1/messages - Send a message
router.post('/', checkMessagingPermission, async (req, res) => {
  try {
    // Validate input
    const { error, value } = sendMessageSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        error_ar: 'خطأ في التحقق',
        details: error.details[0].message
      });
    }

    const { receiver_id, subject, content, priority } = value;
    const sender_id = req.user.id;

    // Get receiver's organization
    const receiverResult = await query(
      'SELECT organization_id FROM users WHERE id = $1',
      [receiver_id]
    );

    if (receiverResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Receiver not found',
        error_ar: 'المستقبل غير موجود'
      });
    }

    const organization_id = receiverResult.rows[0].organization_id;

    // Insert message
    const messageResult = await query(`
      INSERT INTO messages (sender_id, receiver_id, organization_id, subject, content, priority)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [sender_id, receiver_id, organization_id, subject, content, priority]);

    const message = messageResult.rows[0];

    // Create notification for receiver
    await query(`
      INSERT INTO notifications (user_id, title, title_ar, content, content_ar, type, metadata)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      receiver_id,
      'New Message',
      'رسالة جديدة',
      `You have received a new message: ${subject}`,
      `لقد تلقيت رسالة جديدة: ${subject}`,
      'message',
      JSON.stringify({ message_id: message.id })
    ]);

    // Send real-time notification if socket.io is available
    if (global.io) {
      global.io.to(`user_${receiver_id}`).emit('new_message', {
        id: message.id,
        subject: message.subject,
        sender_name: `${req.user.first_name} ${req.user.last_name}`,
        created_at: message.created_at
      });
    }

    logger.info(`Message sent from ${sender_id} to ${receiver_id}`);

    res.status(201).json({
      message: 'Message sent successfully',
      message_ar: 'تم إرسال الرسالة بنجاح',
      data: message
    });

  } catch (error) {
    logger.error('Send message error:', error);
    res.status(500).json({
      error: 'Failed to send message',
      error_ar: 'فشل في إرسال الرسالة'
    });
  }
});

// GET /api/v1/messages/:id - Get specific message
router.get('/:id', async (req, res) => {
  try {
    const messageId = req.params.id;
    const userId = req.user.id;

    const messageResult = await query(`
      SELECT 
        m.*,
        sender.first_name as sender_first_name,
        sender.last_name as sender_last_name,
        sender.first_name_ar as sender_first_name_ar,
        sender.last_name_ar as sender_last_name_ar,
        sender.role as sender_role,
        receiver.first_name as receiver_first_name,
        receiver.last_name as receiver_last_name,
        receiver.first_name_ar as receiver_first_name_ar,
        receiver.last_name_ar as receiver_last_name_ar,
        receiver.role as receiver_role,
        org.name as organization_name,
        org.name_ar as organization_name_ar,
        (
          SELECT json_agg(
            json_build_object(
              'id', ma.id,
              'file_name', ma.file_name,
              'file_path', ma.file_path,
              'file_type', ma.file_type,
              'file_size', ma.file_size
            )
          )
          FROM message_attachments ma
          WHERE ma.message_id = m.id
        ) as attachments
      FROM messages m
      LEFT JOIN users sender ON m.sender_id = sender.id
      LEFT JOIN users receiver ON m.receiver_id = receiver.id
      LEFT JOIN organizations org ON m.organization_id = org.id
      WHERE m.id = $1 AND (m.sender_id = $2 OR m.receiver_id = $2)
    `, [messageId, userId]);

    if (messageResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Message not found',
        error_ar: 'الرسالة غير موجودة'
      });
    }

    const message = messageResult.rows[0];

    // Mark as read if user is the receiver
    if (message.receiver_id === userId && !message.is_read) {
      await query(`
        UPDATE messages 
        SET is_read = true, read_at = CURRENT_TIMESTAMP, status = 'read'
        WHERE id = $1
      `, [messageId]);
      
      message.is_read = true;
      message.read_at = new Date();
      message.status = 'read';
    }

    res.json({
      message: message
    });

  } catch (error) {
    logger.error('Get message error:', error);
    res.status(500).json({
      error: 'Failed to get message',
      error_ar: 'فشل في الحصول على الرسالة'
    });
  }
});

// PUT /api/v1/messages/:id/read - Mark message as read
router.put('/:id/read', async (req, res) => {
  try {
    const messageId = req.params.id;
    const userId = req.user.id;

    const result = await query(`
      UPDATE messages 
      SET is_read = true, read_at = CURRENT_TIMESTAMP, status = 'read'
      WHERE id = $1 AND receiver_id = $2
      RETURNING id, is_read, read_at
    `, [messageId, userId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Message not found or not authorized',
        error_ar: 'الرسالة غير موجودة أو غير مخول'
      });
    }

    res.json({
      message: 'Message marked as read',
      message_ar: 'تم تمييز الرسالة كمقروءة',
      data: result.rows[0]
    });

  } catch (error) {
    logger.error('Mark message as read error:', error);
    res.status(500).json({
      error: 'Failed to mark message as read',
      error_ar: 'فشل في تمييز الرسالة كمقروءة'
    });
  }
});

module.exports = router;
