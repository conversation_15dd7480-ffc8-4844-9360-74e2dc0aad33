const express = require('express');
const { query } = require('../config/database');
const { logger } = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
router.use(authenticateToken);

// GET /api/v1/documents - Get documents
router.get('/', async (req, res) => {
  try {
    const { category, organization_id } = req.query;
    const userId = req.user.id;

    let whereClause = 'WHERE (d.is_public = true OR d.uploaded_by = $1)';
    const queryParams = [userId];

    if (category) {
      queryParams.push(category);
      whereClause += ` AND d.category = $${queryParams.length}`;
    }

    if (organization_id) {
      queryParams.push(organization_id);
      whereClause += ` AND d.organization_id = $${queryParams.length}`;
    }

    const result = await query(`
      SELECT 
        d.*,
        u.first_name as uploader_first_name,
        u.last_name as uploader_last_name,
        u.role as uploader_role,
        o.name as organization_name,
        o.name_ar as organization_name_ar
      FROM documents d
      JOIN users u ON d.uploaded_by = u.id
      JOIN organizations o ON d.organization_id = o.id
      ${whereClause}
      ORDER BY d.created_at DESC
    `, queryParams);

    res.json({
      documents: result.rows
    });

  } catch (error) {
    logger.error('Get documents error:', error);
    res.status(500).json({
      error: 'Failed to get documents',
      error_ar: 'فشل في الحصول على الوثائق'
    });
  }
});

module.exports = router;
