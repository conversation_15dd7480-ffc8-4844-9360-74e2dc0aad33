const jwt = require('jsonwebtoken');
const { query } = require('../config/database');
const { logger } = require('../utils/logger');

// Verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access token required',
        error_ar: 'رمز الوصول مطلوب'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if user still exists and is active
    const userResult = await query(
      'SELECT id, email, role, organization_id, is_active FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        error: 'User not found',
        error_ar: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    if (!user.is_active) {
      return res.status(401).json({
        error: 'Account is deactivated',
        error_ar: 'الحساب معطل'
      });
    }

    // Add user info to request
    req.user = user;
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        error_ar: 'رمز غير صالح'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        error_ar: 'انتهت صلاحية الرمز'
      });
    }

    logger.error('Authentication error:', error);
    res.status(500).json({
      error: 'Authentication failed',
      error_ar: 'فشل في المصادقة'
    });
  }
};

// Check user role permissions
const authorize = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        error_ar: 'المصادقة مطلوبة'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        error_ar: 'صلاحيات غير كافية'
      });
    }

    next();
  };
};

// Check if user can access organization data
const checkOrganizationAccess = async (req, res, next) => {
  try {
    const { organizationId } = req.params;
    const userId = req.user.id;

    // Get user's organization hierarchy
    const hierarchyResult = await query(`
      WITH RECURSIVE org_hierarchy AS (
        -- Get user's organization
        SELECT o.id, o.parent_id, 0 as level
        FROM organizations o
        JOIN users u ON u.organization_id = o.id
        WHERE u.id = $1
        
        UNION ALL
        
        -- Get all child organizations
        SELECT o.id, o.parent_id, oh.level + 1
        FROM organizations o
        JOIN org_hierarchy oh ON o.parent_id = oh.id
        
        UNION ALL
        
        -- Get all parent organizations
        SELECT o.id, o.parent_id, oh.level - 1
        FROM organizations o
        JOIN org_hierarchy oh ON oh.parent_id = o.id
      )
      SELECT id FROM org_hierarchy WHERE id = $2;
    `, [userId, organizationId]);

    if (hierarchyResult.rows.length === 0) {
      return res.status(403).json({
        error: 'Access denied to this organization',
        error_ar: 'الوصول مرفوض لهذه المؤسسة'
      });
    }

    next();

  } catch (error) {
    logger.error('Organization access check error:', error);
    res.status(500).json({
      error: 'Access check failed',
      error_ar: 'فشل في فحص الصلاحيات'
    });
  }
};

// Check if user can send message to target user
const checkMessagingPermission = async (req, res, next) => {
  try {
    const senderId = req.user.id;
    const { receiverId } = req.body;

    if (!receiverId) {
      return res.status(400).json({
        error: 'Receiver ID is required',
        error_ar: 'معرف المستقبل مطلوب'
      });
    }

    // Get sender and receiver organization hierarchy
    const permissionResult = await query(`
      WITH sender_hierarchy AS (
        WITH RECURSIVE org_tree AS (
          SELECT o.id, o.parent_id, 0 as level
          FROM organizations o
          JOIN users u ON u.organization_id = o.id
          WHERE u.id = $1
          
          UNION ALL
          
          SELECT o.id, o.parent_id, ot.level + 1
          FROM organizations o
          JOIN org_tree ot ON o.parent_id = ot.id
          
          UNION ALL
          
          SELECT o.id, o.parent_id, ot.level - 1
          FROM organizations o
          JOIN org_tree ot ON ot.parent_id = o.id
        )
        SELECT id FROM org_tree
      ),
      receiver_org AS (
        SELECT organization_id FROM users WHERE id = $2
      )
      SELECT 1 as can_message
      FROM receiver_org ro
      WHERE ro.organization_id IN (SELECT id FROM sender_hierarchy);
    `, [senderId, receiverId]);

    if (permissionResult.rows.length === 0) {
      return res.status(403).json({
        error: 'Cannot send message to this user',
        error_ar: 'لا يمكن إرسال رسالة لهذا المستخدم'
      });
    }

    next();

  } catch (error) {
    logger.error('Messaging permission check error:', error);
    res.status(500).json({
      error: 'Permission check failed',
      error_ar: 'فشل في فحص الصلاحيات'
    });
  }
};

module.exports = {
  authenticateToken,
  authorize,
  checkOrganizationAccess,
  checkMessagingPermission
};
