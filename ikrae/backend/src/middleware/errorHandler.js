const { logger } = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  logger.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    const message_ar = 'المورد غير موجود';
    error = { message, message_ar };
    return res.status(404).json({
      error: message,
      error_ar: message_ar
    });
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    const message_ar = 'تم إدخال قيمة مكررة';
    error = { message, message_ar };
    return res.status(400).json({
      error: message,
      error_ar: message_ar
    });
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    const message_ar = 'خطأ في التحقق من البيانات';
    error = { message, message_ar };
    return res.status(400).json({
      error: message,
      error_ar: message_ar
    });
  }

  // PostgreSQL errors
  if (err.code) {
    switch (err.code) {
      case '23505': // Unique violation
        return res.status(409).json({
          error: 'Duplicate entry',
          error_ar: 'إدخال مكرر'
        });
      case '23503': // Foreign key violation
        return res.status(400).json({
          error: 'Invalid reference',
          error_ar: 'مرجع غير صالح'
        });
      case '23502': // Not null violation
        return res.status(400).json({
          error: 'Required field missing',
          error_ar: 'حقل مطلوب مفقود'
        });
      default:
        break;
    }
  }

  res.status(error.statusCode || 500).json({
    error: error.message || 'Server Error',
    error_ar: 'خطأ في الخادم'
  });
};

module.exports = errorHandler;
