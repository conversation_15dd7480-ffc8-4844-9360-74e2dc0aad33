const { Pool } = require('pg');
const { logger } = require('../utils/logger');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'ikrae_db',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || '',
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

// Create connection pool
const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err) => {
  logger.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Database connection function
const connectDB = async () => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    client.release();
    
    logger.info(`Database connected successfully at ${result.rows[0].now}`);
    return true;
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
};

// Query function with error handling
const query = async (text, params) => {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    if (duration > 1000) {
      logger.warn(`Slow query detected (${duration}ms): ${text}`);
    }
    
    return result;
  } catch (error) {
    logger.error('Database query error:', {
      query: text,
      params: params,
      error: error.message
    });
    throw error;
  }
};

// Transaction helper
const transaction = async (callback) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

// Get user with organization hierarchy
const getUserWithHierarchy = async (userId) => {
  const queryText = `
    WITH RECURSIVE org_hierarchy AS (
      -- Base case: get user's organization
      SELECT o.id, o.name, o.name_ar, o.type, o.parent_id, 0 as level
      FROM organizations o
      JOIN users u ON u.organization_id = o.id
      WHERE u.id = $1
      
      UNION ALL
      
      -- Recursive case: get parent organizations
      SELECT o.id, o.name, o.name_ar, o.type, o.parent_id, oh.level + 1
      FROM organizations o
      JOIN org_hierarchy oh ON o.id = oh.parent_id
    )
    SELECT 
      u.*,
      json_agg(
        json_build_object(
          'id', oh.id,
          'name', oh.name,
          'name_ar', oh.name_ar,
          'type', oh.type,
          'level', oh.level
        ) ORDER BY oh.level
      ) as organization_hierarchy
    FROM users u
    LEFT JOIN org_hierarchy oh ON true
    WHERE u.id = $1
    GROUP BY u.id;
  `;
  
  const result = await query(queryText, [userId]);
  return result.rows[0];
};

// Get users by organization hierarchy (for messaging permissions)
const getUsersByHierarchy = async (organizationId, direction = 'down') => {
  let queryText;
  
  if (direction === 'down') {
    // Get all users in child organizations
    queryText = `
      WITH RECURSIVE org_hierarchy AS (
        SELECT id, parent_id FROM organizations WHERE id = $1
        UNION ALL
        SELECT o.id, o.parent_id 
        FROM organizations o
        JOIN org_hierarchy oh ON o.parent_id = oh.id
      )
      SELECT u.*, o.name as organization_name, o.name_ar as organization_name_ar
      FROM users u
      JOIN organizations o ON u.organization_id = o.id
      WHERE o.id IN (SELECT id FROM org_hierarchy)
      AND u.is_active = true
      ORDER BY o.type, u.role, u.last_name;
    `;
  } else {
    // Get all users in parent organizations
    queryText = `
      WITH RECURSIVE org_hierarchy AS (
        SELECT id, parent_id FROM organizations WHERE id = $1
        UNION ALL
        SELECT o.id, o.parent_id 
        FROM organizations o
        JOIN org_hierarchy oh ON oh.parent_id = o.id
      )
      SELECT u.*, o.name as organization_name, o.name_ar as organization_name_ar
      FROM users u
      JOIN organizations o ON u.organization_id = o.id
      WHERE o.id IN (SELECT id FROM org_hierarchy)
      AND u.is_active = true
      ORDER BY o.type, u.role, u.last_name;
    `;
  }
  
  const result = await query(queryText, [organizationId]);
  return result.rows;
};

module.exports = {
  pool,
  connectDB,
  query,
  transaction,
  getUserWithHierarchy,
  getUsersByHierarchy
};
